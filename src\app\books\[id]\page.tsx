import type { Metada<PERSON> } from 'next';

// Updated type for Next.js 15 - params is now a Promise
type Props = {
  params: Promise<{ id: string }>;
};

export async function generateMetadata(
  { params }: Props
): Promise<Metadata> {
  // Await the params Promise in Next.js 15
  const resolvedParams = await params;

  // Fetch data if needed
  return {
    title: `Book: ${resolvedParams.id}`,
    // other metadata
  };
}

export default async function BookPage({ params }: Props) {
  // Await the params Promise in Next.js 15
  const resolvedParams = await params;

  // Your component code here
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold">Book ID: {resolvedParams.id}</h1>
      {/* Add your book detail implementation here */}
    </div>
  );
}