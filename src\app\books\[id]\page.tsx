import type { Metadata } from 'next';

type Props = {
  params: { id: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

export async function generateMetadata(
  { params, searchParams }: Props
): Promise<Metadata> {
  // Fetch data if needed
  return {
    title: `Book: ${params.id}`,
    // other metadata
  };
}

export default function BookPage({ params, searchParams }: Props) {
  // Your component code
}