import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { fetchBookById } from '../../../utils/api';
import PurchaseForm from '../../components/PurchaseForm';

export default function BookDetail() {
  const { query } = useRouter();
  const [book, setBook] = useState<any>(null);

  useEffect(() => {
    if (query.id) {
      fetchBookById(query.id as string).then(setBook);
    }
  }, [query.id]);

  if (!book) return <p className="p-6">Loading...</p>;

  const { title, authors, description, imageLinks, publisher, categories } = book.volumeInfo;
  const price = book.saleInfo?.listPrice?.amount;

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex flex-col md:flex-row gap-6">
        <img src={imageLinks?.thumbnail} className="w-48 h-auto rounded" alt={title} />
        <div>
          <h2 className="text-2xl font-bold">{title}</h2>
          <p className="text-gray-700">By {authors?.join(', ')}</p>
          <p className="mt-2 text-sm text-gray-500">Publisher: {publisher}</p>
          <p className="text-sm text-gray-500">Categories: {categories?.join(', ')}</p>
          <p className="text-lg mt-4">
            💰 <strong>Price:</strong> {price ? `$${price}` : 'Not for Sale'}
          </p>
          <PurchaseForm bookTitle={title} />
        </div>
      </div>
      <div className="mt-6">
        <h3 className="text-lg font-semibold">Description</h3>
        <p className="text-sm mt-2">{description || 'No description available.'}</p>
      </div>
    </div>
  );
}
