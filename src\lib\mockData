/*import { Book } from '../app/pages'

export const mockBooks: Book[] = [
  {
    id: '1',
    title: 'The Great Gatsby',
    author: '<PERSON><PERSON>',
    price: 12.99,
    genre: 'Classic',
    coverUrl: '/covers/great-gatsby.jpg',
    description: 'A story of wealth, love, and the American Dream in the 1920s.'
  },
  {
    id: '2',
    title: 'To Kill a Mockingbird',
    author: '<PERSON> Lee',
    price: 10.99,
    genre: 'Fiction',
    coverUrl: '/covers/mockingbird.jpg',
    description: 'A powerful story of racial injustice and moral growth.'
  },
  // Add 10+ more books...
]

export async function fetchBooks(query: string): Promise<Book[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // Update "last searched" for notifications
  const results = mockBooks.filter(book => {
    const matches = book.title.toLowerCase().includes(query.toLowerCase()) || 
                   book.author.toLowerCase().includes(query.toLowerCase())
    if (matches) book.lastSearched = new Date()
    return matches
  })

  return results
}

export async function getBookById(id: string): Promise<Book | undefined> {
  await new Promise(resolve => setTimeout(resolve, 200))
  return mockBooks.find(book => book.id === id)
}*/