import { useState } from 'react';
import { fetchBooks } from '../../utils/api';
import BookCard from '../components/BookCard';

export default function Home() {
  const [query, setQuery] = useState('');
  const [books, setBooks] = useState([]);

  const handleSearch = async () => {
    const results = await fetchBooks(query);
    setBooks(results);
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">📚 Book Finder</h1>
      <div className="flex gap-2 mb-6">
        <input
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search books..."
          className="flex-1 p-2 border rounded"
        />
        <button onClick={handleSearch} className="bg-blue-500 text-white px-4 py-2 rounded">
          Search
        </button>
      </div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {books.map((book: any) => (
          <BookCard key={book.id} book={book} />
        ))}
      </div>
    </div>
  );
}
