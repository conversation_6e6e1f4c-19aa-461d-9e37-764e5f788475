'use client';

import { useEffect, useState } from 'react';
import { applyTheme, toggleTheme } from '../../utils/theme';

export default function ThemeToggle() {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    applyTheme();
    setIsDark(document.documentElement.classList.contains('dark'));
  }, []);

  const handleToggle = () => {
    toggleTheme();
    setIsDark(!isDark);
  };

  return (
    <button
      onClick={handleToggle}
      className="ml-4 p-2 rounded-full bg-gray-200 dark:bg-gray-700"
      aria-label="Toggle Theme"
    >
      {isDark ? '🌙' : '☀️'}
    </button>
  );
}
