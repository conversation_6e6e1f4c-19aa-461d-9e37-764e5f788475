'use client'

import { useState } from 'react'

export default function PurchaseForm({ bookTitle }: { bookTitle: string }) {
  const [option, setOption] = useState('hardcopy')
  const [submitted, setSubmitted] = useState(false)

  const handleSubmit = () => {
    console.log(`Purchase interest: ${bookTitle}, Option: ${option}`)
    localStorage.setItem(`purchase_${bookTitle}`, option)
    setSubmitted(true)
  }

  return submitted ? (
    <p className="text-green-600 mt-4">✅ Thank you! We've notified the author of your interest.</p>
  ) : (
    <div className="mt-4 space-y-2">
      <label className="block font-medium">Choose a format:</label>
      <select className="p-2 border rounded" onChange={(e) => setOption(e.target.value)}>
        <option value="hardcopy">Hardcopy</option>
        <option value="softcopy">Softcopy (PDF/eBook)</option>
      </select>
      <button onClick={handleSubmit} className="bg-blue-600 text-white px-4 py-2 rounded mt-2 hover:bg-blue-700">
        Notify Author
      </button>
    </div>
  )
}
