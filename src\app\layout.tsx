import type { Metadata } from 'next'
import './globals.css'
import Navbar from './components/NavBar'

export const metadata: Metadata = {
  title: 'Book Finder Dashboard',
  description: 'Find, explore and purchase books easily',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="bg-gray-100">
        <Navbar />
        {children}
      </body>
    </html>
  )
}


