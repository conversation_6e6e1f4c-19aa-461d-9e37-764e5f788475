'use client';
import { useEffect, useState } from 'react';

export default function ProfilePage() {
  const [user, setUser] = useState('');

  useEffect(() => {
    setUser(localStorage.getItem('user') || '');
  }, []);

  return (
    <div className="max-w-md mx-auto p-6">
      <h2 className="text-2xl font-bold mb-4">Your Profile</h2>
      {user ? (
        <p>Email: {user}</p>
      ) : (
        <p className="text-red-500">You are not signed in.</p>
      )}
    </div>
  );
}
