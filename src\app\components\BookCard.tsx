'use client';

import Link from 'next/link';
import { Star } from 'lucide-react';

interface BookCardProps {
  book: {
    id: string;
    volumeInfo: {
      title: string;
      authors?: string[];
      imageLinks?: {
        thumbnail?: string;
      };
      averageRating?: number;
      ratingsCount?: number;
      description?: string;
    };
  };
}

export default function BookCard({ book }: BookCardProps) {
  const { title, authors, imageLinks, averageRating, ratingsCount } = book.volumeInfo;
  const thumbnail = imageLinks?.thumbnail;
  
  // Get the first 2 authors and add "& others" if there are more
  const displayAuthors = authors 
    ? authors.length > 2 
      ? `${authors[0]}, ${authors[1]} & others` 
      : authors.join(', ')
    : 'Unknown Author';
    
  return (
    <Link 
      href={`/book/${book.id}`} 
      className="flex flex-col h-full p-4 bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500"
    >
      <div className="relative w-full h-48 mb-4 overflow-hidden rounded-lg">
        <img 
          src={thumbnail} 
          alt={title} 
          className="w-full h-full object-cover transition-transform duration-300 hover:scale-110"
          loading="lazy"
        />
      </div>
      
      <div className="flex flex-col flex-grow">
        <h3 className="text-lg font-semibold line-clamp-2 mb-1">{title}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-auto line-clamp-1">{displayAuthors}</p>
        
        {averageRating && (
          <div className="flex items-center mt-2">
            <div className="flex mr-1">
              {[...Array(5)].map((_, i) => (
                <Star 
                  key={i} 
                  className={`w-4 h-4 ${
                    i < Math.floor(averageRating) 
                      ? "fill-yellow-400 text-yellow-400" 
                      : i < averageRating 
                        ? "fill-yellow-400 text-yellow-400 opacity-50" 
                        : "text-gray-300"
                  }`}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {averageRating.toFixed(1)}
              {ratingsCount && ` (${ratingsCount})`}
            </span>
          </div>
        )}
      </div>
    </Link>
  );
}