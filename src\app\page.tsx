'use client';

import { useState, useEffect } from 'react';
import { fetchBooks } from '../utils/api';
import HeroSection from './components/HeroSection';
import BookCard from './components/BookCard';
import SectionTitle from './components/SectionTitle';

// Define proper page props type
type PageProps = {
  params?: { [key: string]: string | string[] };
  searchParams?: { [key: string]: string | string[] };
};

export default function Home({ params, searchParams }: PageProps) {
  const [books, setBooks] = useState([]);
  const [query, setQuery] = useState('');

  useEffect(() => {
    fetch('/dummy-books.json')
      .then((res) => res.json())
      .then(setBooks);
  }, []);

  const handleSearch = async () => {
    const results = await fetchBooks(query);
    setBooks(results);
  };

  return (
    <div>
      <HeroSection />
      <div className="p-6 max-w-6xl mx-auto">
        <div className="flex gap-2 mb-6">
          <input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search books..."
            className="flex-1 p-2 border rounded"
          />
          <button onClick={handleSearch} className="bg-blue-500 text-white px-4 py-2 rounded">
            Search
          </button>
        </div>

        <SectionTitle title="Trending Books" />
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-10">
          {books.map((book: any) => (
            <BookCard key={book.id} book={book} />
          ))}
        </div>

        <SectionTitle title="New Releases" />
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-10">
          {books.map((book: any) => (
            <BookCard key={book.id} book={book} />
          ))}
        </div>

        <SectionTitle title="Design & Art Books" />
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {books.map((book: any) => (
            <BookCard key={book.id} book={book} />
          ))}
        </div>
      </div>
    </div>
  );
}




