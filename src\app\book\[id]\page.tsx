'use client';

import { fetchBookById } from '../../../utils/api';
import { useEffect, useState } from 'react';

export default function BookDetail({ params }: { params: { id: string } }) {
  const [book, setBook] = useState<any>(null);

  useEffect(() => {
    fetchBookById(params.id).then(setBook);
  }, [params.id]);

  if (!book) return <p className="p-6">Loading...</p>;

  const { title, authors, description, imageLinks } = book.volumeInfo;
  const price = book.saleInfo?.listPrice?.amount;

  return (
    <div className="p-6 max-w-4xl mx-auto dark:text-white">
      <img src={imageLinks?.thumbnail || '/no-cover.png'} className="w-48 rounded" alt={title} />
      <h2 className="text-3xl font-bold mt-4">{title}</h2>
      <p className="text-gray-600 dark:text-gray-400">By {authors?.join(', ')}</p>
      <p className="mt-2">💰 Price: {price ? `$${price}` : 'Not for Sale'}</p>
      <p className="mt-4">{description || 'No description available.'}</p>
    </div>
  );
}
