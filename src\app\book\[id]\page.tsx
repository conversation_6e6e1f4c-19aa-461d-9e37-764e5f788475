'use client';

import { fetchBookById } from '../../../utils/api';
import { useEffect, useState } from 'react';

// Define the correct type for Next.js 15 params
type PageProps = {
  params: Promise<{ id: string }>;
};

export default function BookDetail({ params }: PageProps) {
  const [book, setBook] = useState<any>(null);
  const [bookId, setBookId] = useState<string | null>(null);

  // Handle the Promise-based params in Next.js 15
  useEffect(() => {
    params.then((resolvedParams) => {
      setBookId(resolvedParams.id);
    });
  }, [params]);

  useEffect(() => {
    if (bookId) {
      fetchBookById(bookId).then(setBook);
    }
  }, [bookId]);

  if (!book) return <p className="p-6">Loading...</p>;

  const { title, authors, description, imageLinks } = book.volumeInfo;
  const price = book.saleInfo?.listPrice?.amount;

  return (
    <div className="p-6 max-w-4xl mx-auto dark:text-white">
      <img src={imageLinks?.thumbnail || '/no-cover.png'} className="w-48 rounded" alt={title} />
      <h2 className="text-3xl font-bold mt-4">{title}</h2>
      <p className="text-gray-600 dark:text-gray-400">By {authors?.join(', ')}</p>
      <p className="mt-2">💰 Price: {price ? `$${price}` : 'Not for Sale'}</p>
      <p className="mt-4">{description || 'No description available.'}</p>
    </div>
  );
}
