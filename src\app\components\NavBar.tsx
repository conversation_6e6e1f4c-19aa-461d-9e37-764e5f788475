'use client';

import Link from 'next/link';
import Image from 'next/image';
import ThemeToggle from '../components/ThemeToggle';
import { useState } from 'react';

export default function Navbar() {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  return (
    <nav className="bg-white dark:bg-gray-800 shadow">
      <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/" className="text-xl font-bold text-gray-800 dark:text-white">
            📚 Book Finder
          </Link>
          <Link href="/profile" className="text-gray-600 dark:text-gray-300 hover:underline">
            Profile
          </Link>
          <Link href="/signin" className="text-gray-600 dark:text-gray-300 hover:underline">
            Sign In
          </Link>
          <Link href="/signup" className="text-gray-600 dark:text-gray-300 hover:underline">
            Sign Up
          </Link>
        </div>
        <div className="flex items-center">
          <ThemeToggle />
          <div className="ml-4 relative">
            <button
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="flex items-center focus:outline-none"
            >
              <Image
                src="https://i.pravatar.cc/40"
                alt="Profile"
                width={40}
                height={40}
                className="rounded-full"
              />
            </button>
            {dropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg py-2 z-20">
                <Link
                  href="/profile"
                  className="block px-4 py-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600"
                >
                  View Profile
                </Link>
                <button
                  onClick={() => alert('Signed out')}
                  className="w-full text-left px-4 py-2 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600"
                >
                  Sign Out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
