export interface Book {
  id: string
  title: string
  author: string
  price: number
  coverUrl?: string
  volumeInfo: {
    title: string
    authors: string[]
    description: string
    imageLinks: {
      thumbnail: string
    }
    publisher: string
    categories: string[]
    publishedDate: string
    industryIdentifiers: {
      type: string
      identifier: string
    }[]
  }
  saleInfo: {
    listPrice: {
      amount: number
      currencyCode: string
    }
  }
}
