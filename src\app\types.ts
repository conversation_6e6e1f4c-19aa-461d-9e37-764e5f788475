// Google Books API response structure
export interface GoogleBook {
  id: string;
  volumeInfo: {
    title: string;
    authors?: string[];
    description?: string;
    imageLinks?: {
      thumbnail?: string;
      small?: string;
      medium?: string;
      large?: string;
    };
    publisher?: string;
    categories?: string[];
    publishedDate?: string;
    averageRating?: number;
    ratingsCount?: number;
    industryIdentifiers?: {
      type: string;
      identifier: string;
    }[];
  };
  saleInfo?: {
    listPrice?: {
      amount: number;
      currencyCode: string;
    };
    retailPrice?: {
      amount: number;
      currencyCode: string;
    };
  };
}

// Legacy Book interface (keeping for compatibility)
export interface Book {
  id: string
  title: string
  author: string
  price: number
  coverUrl?: string
  volumeInfo: {
    title: string
    authors: string[]
    description: string
    imageLinks: {
      thumbnail: string
    }
    publisher: string
    categories: string[]
    publishedDate: string
    industryIdentifiers: {
      type: string
      identifier: string
    }[]
  }
  saleInfo: {
    listPrice: {
      amount: number
      currencyCode: string
    }
  }
}
